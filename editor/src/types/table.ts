export interface TableCellBorder {
	width: number; // in px
	color: string | null; // hex string, or null to use table default
}

export interface TableCell {
	content: string;
	colspan: number;
	rowspan: number;
	backgroundColor: string | null;
	verticalAlign?: "top" | "middle" | "bottom";
	borderSettings?: {
		// Optional to allow old data to still work initially
		top?: TableCellBorder;
		right?: TableCellBorder;
		bottom?: TableCellBorder;
		left?: TableCellBorder;
	};
	// Old structure, for migration or if some parts still use it.
	// Consider removing after full migration to borderSettings.
	borderWidths: {
		top: number;
		right: number;
		bottom: number;
		left: number;
	};
}

export interface TableProperties {
	rows: number;
	columns: number;
	borderWidth: number;
	borderStyle: string;
	borderColor?: string;
	backgroundColor?: string;
	cells: TableCell[][];
	columnWidths?: number[];
	rowHeights?: number[];
	selectedCellsBackgroundColor?: string;
	selection?: {
		start: { row: number; col: number };
		end: { row: number; col: number };
	} | null;
}
